import { yup<PERSON>esolver } from "@hookform/resolvers/yup";
import { BlockStack, Box, Button, Card, Text, TextField, toast } from "@storeware/polaris";
import { Controller, useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import type { ILoginUserPayload } from "~/server/types/auth";
import type { Route } from "../../.react-router/types/app/routes/+types/login";
import { useLoginUser } from "../../api/auth";
import { useAuth } from "../../contexts/AuthContext";
import { loginSchema } from "../../utils/validation";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "StoreSEO Agency - Login" },
    { name: "description", content: "Sign in to your StoreSEO Agency account" },
  ];
}

export default function Login() {
  const navigate = useNavigate();
  const { checkAuth, workspace, user, isAuthenticated, isLoading, login, userProfile } = useAuth();

  console.log("user", user, "workspace", workspace, isAuthenticated);

  const { mutate: handleLogin } = useLoginUser();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
  } = useForm<ILoginUserPayload>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: ILoginUserPayload) => {
    try {
      clearErrors(); // Clear any previous errors

      handleLogin(data, {
        onSuccess: async () => {
          // userProfile();

          // if (isAuthenticated && !workspace) {
          //   console.log("hiting setup workspace");
          //   navigate("/setup-workspace");
          // }

          // console.log("hiting dashboard");

          navigate("/dashboard");
        },
      });
    } catch (error) {
      console.error("Login failed:", error);
      setError("root", { message: "An unexpected error occurred. Please try again." });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8">
        <Box>
          <Card>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Text
                as="h1"
                variant="headingLg"
                alignment="center"
              >
                Sign In
              </Text>
              <Text
                as="p"
                variant="bodyMd"
                tone="subdued"
                alignment="center"
              >
                Welcome back to StoreSEO Agency
              </Text>

              {/* General Error Display */}
              {errors.root && (
                <Box paddingBlockStart="400">
                  <Text
                    as="p"
                    tone="critical"
                    variant="bodyMd"
                  >
                    {errors.root.message}
                  </Text>
                </Box>
              )}

              {/* Form Fields */}
              <Box
                paddingBlockStart="400"
                paddingBlockEnd="400"
              >
                <BlockStack gap="400">
                  <Controller
                    name="email"
                    control={control}
                    render={({ field, fieldState }) => (
                      <TextField
                        label="Email"
                        type="email"
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        error={fieldState.error?.message}
                        autoComplete="email"
                        placeholder="Enter your email address"
                        disabled={isSubmitting}
                      />
                    )}
                  />

                  <Controller
                    name="password"
                    control={control}
                    render={({ field, fieldState }) => (
                      <TextField
                        label="Password"
                        type="password"
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        error={fieldState.error?.message}
                        autoComplete="current-password"
                        placeholder="Enter your password"
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </BlockStack>
              </Box>

              {/* Submit Button */}
              <Button
                variant="primary"
                size="large"
                fullWidth
                submit
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Signing In..." : "Sign In"}
              </Button>

              {/* Register Link */}
              <Box paddingBlockStart="400">
                <Text
                  as="p"
                  variant="bodyMd"
                  alignment="center"
                >
                  Don't have an account?{" "}
                  <Button
                    variant="plain"
                    onClick={() => navigate("/register")}
                    disabled={isSubmitting}
                  >
                    Create account
                  </Button>
                </Text>
              </Box>
            </form>
          </Card>
        </Box>
      </div>
    </div>
  );
}
