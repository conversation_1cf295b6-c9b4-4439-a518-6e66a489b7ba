// import { BlockStack, Box, Card, Text } from "@storeware/polaris";
// import { useEffect, useRef, type ReactNode } from "react";
// import { Navigate } from "react-router";
// import { useAuth } from "../contexts/AuthContext";

// interface ProtectedRouteProps {
//   children: ReactNode;
// }

// export function ProtectedRoute({ children }: ProtectedRouteProps) {
//   const { isAuthenticated, isLoading, user, workspace,  } = useAuth();
//   const requestedAuth = useRef(false);

//   // Kick off auth check only for protected pages
//   // useEffect(() => {
//   //   if (!requestedAuth.current) {
//   //     requestedAuth.current = true;
//   //     checkAuth();
//   //   }
//   // }, [checkAuth]);

//   // While we haven't initiated the check yet OR while global auth is loading, show loader
//   if (!requestedAuth.current || isLoading) {
//     return (
//       <div className="min-h-screen flex items-center justify-center bg-gray-50">
//         <Card>
//           <Box padding="600">
//             <BlockStack
//               gap="400"
//               align="center"
//             >
//               <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center animate-pulse">
//                 <Text
//                   as="span"
//                   variant="headingLg"
//                 >
//                   ⏳
//                 </Text>
//               </div>
//               <Text
//                 as="h1"
//                 variant="headingLg"
//                 alignment="center"
//               >
//                 Loading...
//               </Text>
//               <Text
//                 as="p"
//                 variant="bodyMd"
//                 tone="subdued"
//                 alignment="center"
//               >
//                 Please wait while we check your authentication status.
//               </Text>
//             </BlockStack>
//           </Box>
//         </Card>
//       </div>
//     );
//   }

//   // Redirect to login if not authenticated
//   if (!isAuthenticated) {
//     return (
//       <Navigate
//         to="/login"
//         replace
//       />
//     );
//   }

//   // Check if user needs to complete workspace setup
//   if (user && !workspace) {
//     return (
//       <Navigate
//         to="/setup-workspace"
//         replace
//       />
//     );
//   }

//   // Render protected content
//   return <>{children}</>;
// }

// export default ProtectedRoute;

import { BlockStack, Box, Card, Text } from "@storeware/polaris";
import { useEffect, useRef, type ReactNode } from "react";
import { Navigate } from "react-router";
import { useAuth } from "../contexts/AuthContext";

interface ProtectedRouteProps {
  children: ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user, workspace, userProfile } = useAuth();
  const requestedAuth = useRef(false);

  console.log("user", user);

  // Only run checkAuth once when this protected route mounts
  useEffect(() => {
    if (!requestedAuth.current) {
      requestedAuth.current = true;
      userProfile(); // This will attempt to fetch profile based on httpOnly cookie
    }
  }, [userProfile]);

  // Show loader until we've run checkAuth AND finished loading
  if (!requestedAuth.current || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card>
          <Box padding="600">
            <BlockStack
              gap="400"
              align="center"
            >
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center animate-pulse">
                <Text
                  as="span"
                  variant="headingLg"
                >
                  ⏳
                </Text>
              </div>
              <Text
                as="h1"
                variant="headingLg"
                alignment="center"
              >
                Loading...
              </Text>
              <Text
                as="p"
                variant="bodyMd"
                tone="subdued"
                alignment="center"
              >
                Please wait while we check your authentication status.
              </Text>
            </BlockStack>
          </Box>
        </Card>
      </div>
    );
  }

  // If not authenticated, send to login
  if (!isAuthenticated) {
    return (
      <Navigate
        to="/login"
        replace
      />
    );
  }

  // If authenticated but no workspace, send to setup page
  if (user && !workspace) {
    return (
      <Navigate
        to="/setup-workspace"
        replace
      />
    );
  }

  // Authenticated + workspace ready → render page
  return <>{children}</>;
}

export default ProtectedRoute;
