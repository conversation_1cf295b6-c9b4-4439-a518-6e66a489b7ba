import { IInvitationTokenPayload, IUser } from "@/types/auth.js";
import { NextFunction, Request, Response } from "express";
import httpStatus from "http-status";
import passport from "passport";
import ApiError from "../utils/apiError.js";

/**
 * Extended Request interface to include user information
 */
export interface AuthenticatedRequest extends Request {
  user: IUser;
}

/**
 * Extended Request interface to include invitation information
 */
export interface InvitationRequest extends Request {
  invitation: IInvitationTokenPayload;
}

/**
 * Auth middleware that extracts user information from JWT token
 * and attaches it to the request object
 */
const auth = () => {
  return (req: Request, res: Response, next: NextFunction): void => {
    passport.authenticate("jwt-access", { session: false }, (err: any, user: any, info: any) => {
      if (err) {
        return next(new ApiError(httpStatus.UNAUTHORIZED, "Authentication failed"));
      }

      if (!user) {
        const message = info?.message || "Invalid or expired token";
        console.log("message", message);
        return next(new ApiError(httpStatus.UNAUTHORIZED, message));
      }

      req.user = user;
      next();
    })(req, res, next);
  };
};

/**
 * Verification auth middleware that if the verification token is valid
 */
const verificationAuth = () => {
  return (req: Request, res: Response, next: NextFunction): void => {
    passport.authenticate("jwt-verification", { session: false }, (err: any, user: IUser, info: any) => {
      if (err) {
        return next(new ApiError(httpStatus.UNAUTHORIZED, "Authentication failed"));
      }

      if (!user) {
        const message = info?.message || "Invalid or expired token";
        return next(new ApiError(httpStatus.UNAUTHORIZED, message));
      }

      req.user = user;
      next();
    })(req, res, next);
  };
};

/**
 * Invitation auth middleware that verifies invitation tokens using passport
 */
const invitationAuth = () => {
  return (req: Request, res: Response, next: NextFunction): void => {
    passport.authenticate(
      "jwt-invitation",
      { session: false },
      (err: any, invitation: IInvitationTokenPayload, info: any) => {
        if (err) {
          return next(new ApiError(httpStatus.UNAUTHORIZED, "Authentication failed"));
        }

        if (!invitation) {
          const message = info?.message || "Invalid or expired invitation token";
          return next(new ApiError(httpStatus.UNAUTHORIZED, message));
        }

        // Attach invitation data to request
        (req as InvitationRequest).invitation = invitation;
        next();
      }
    )(req, res, next);
  };
};

export { auth, invitationAuth, verificationAuth };
